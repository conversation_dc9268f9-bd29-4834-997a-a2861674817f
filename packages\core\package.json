{"name": "koishi-plugin-yesimbot", "description": "Yes! I'm <PERSON><PERSON>! 机械壳，人类心", "version": "3.0.0-beta.4", "main": "lib/index.js", "typings": "lib/index.d.ts", "homepage": "https://github.com/HydroGest/YesImBot", "files": ["lib", "dist", "resources"], "contributors": ["HydroGest <<EMAIL>>", "Dispure <<EMAIL>>"], "scripts": {"build": "tsc && tsc-alias && node scripts/bundle.mjs", "dev": "tsc -w --preserveWatchOutput", "lint": "eslint . --ext .ts", "clean": "rm -rf lib .turbo tsconfig.tsbuildinfo"}, "license": "MIT", "keywords": ["chatbot", "koishi", "plugin", "ai"], "repository": {"type": "git", "url": "git+https://github.com/HydroGest/YesImBot.git", "directory": "packages/core"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json", "./services": {"types": "./lib/services/index.d.ts", "import": "./lib/services/index.mjs", "require": "./lib/services/index.js"}, "./shared": {"types": "./lib/shared/index.d.ts", "import": "./lib/shared/index.mjs", "require": "./lib/shared/index.js"}}, "dependencies": {"gray-matter": "^4.0.3", "jsonrepair": "^3.12.0", "lru-cache": "^11.1.0", "mustache": "^4.2.0", "reflect-metadata": "^0.2.2"}, "devDependencies": {"@xsai-ext/providers-cloud": "^0.3.0-beta.8", "@xsai-ext/providers-local": "^0.3.0-beta.8", "@xsai-ext/shared-providers": "^0.3.0-beta.8", "koishi": "^4.18.7", "koishi-plugin-adapter-onebot": "^6.8.0", "xsai": "^0.3.0-beta.8"}, "peerDependencies": {"koishi": "^4.18.7"}, "koishi": {"description": {"zh": "让语言大模型机器人假装群友并和群友聊天！", "en": "A Koishi plugin that allows LLM chat in your guild."}, "browser": true, "service": {"required": ["database"], "implements": ["yesimbot"]}}}